import React from 'react';
import { Badge } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';

/**
 * StatusBadge - 通用状态徽章组件
 * 
 * @param {string} status - 状态值
 * @param {string} type - 徽章类型，支持 'review', 'order', 'withdrawal', 'distribution'
 * @param {object} customMapping - 自定义状态映射（可选）
 */
const StatusBadge = ({ status, type = 'review', customMapping = null }) => {
    const { t } = useTranslation();

    // 默认的状态映射配置
    const statusMappings = {
        // 审核状态 (review) - 用于KYC、产品审核、订单审核等
        review: {
            'approved': { bg: 'success', key: 'approved' },
            'pending': { bg: 'warning', key: 'pending_review' },
            'rejected': { bg: 'danger', key: 'rejected' },
            'under_review': { bg: 'info', key: 'under_review' },
            'not_submitted': { bg: 'secondary', key: 'not_submitted' }
        },
        
        // 订单状态 (order) - 用于订单处理
        order: {
            'approved': { bg: 'success', key: 'approved' },
            'pending': { bg: 'warning', key: 'pending_review' },
            'rejected': { bg: 'danger', key: 'rejected' },
            'under_review': { bg: 'info', key: 'under_review' },
            'completed': { bg: 'primary', key: 'completed' }
        },
        
        // 提现状态 (withdrawal) - 用于提现申请
        withdrawal: {
            'approved': { bg: 'success', key: 'approved' },
            'pending': { bg: 'warning', key: 'pending_review' },
            'rejected': { bg: 'danger', key: 'rejected' },
            'processing': { bg: 'info', key: 'processing' },
            'completed': { bg: 'primary', key: 'completed' }
        },
        
        // 分发状态 (distribution) - 用于订单分发
        distribution: {
            'completed': { bg: 'success', key: 'completed' },
            'pending': { bg: 'warning', key: 'pending' },
            'processing': { bg: 'info', key: 'processing' },
            'failed': { bg: 'danger', key: 'failed' },
            'cancelled': { bg: 'secondary', key: 'cancelled' }
        },
        
        // 批次状态 (batch) - 用于币批次
        batch: {
            'pending': { bg: 'warning', key: 'pending' },
            'distributed': { bg: 'success', key: 'distributed' },
            'failed': { bg: 'danger', key: 'failed' },
            'cancelled': { bg: 'secondary', key: 'cancelled' }
        },

        // (journal) 
        journal: {
            'deposit': { bg: 'success', key: 'deposit' },
            'withdrawal': { bg: 'danger', key: 'withdrawal' },
            'adjustment': { bg: 'warning', key: 'adjustment' },
            'bonus': { bg: 'info', key: 'bonus' },
            'penalty': { bg: 'dark', key: 'penalty' }
        }
    };

    // 使用自定义映射或默认映射
    const mapping = customMapping || statusMappings[type] || statusMappings.review;
    
    // 获取状态配置
    const statusConfig = mapping[status];
    
    if (statusConfig) {
        return <Badge bg={statusConfig.bg}>{t(statusConfig.key)}</Badge>;
    }
    
    // 默认情况
    return <Badge bg="secondary">{status || '-'}</Badge>;
};

export default StatusBadge;
