import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Form, InputGroup, Dropdown, Modal, Alert, Pagination } from 'react-bootstrap';
import {FaPlus, FaUserCheck, FaExchangeAlt, FaCheck, FaTimes, FaDownload } from 'react-icons/fa';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import StatusBadge from '../../components/StatusBadge';

const Members = () => {
    const { t } = useTranslation();
    const [members, setMembers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [filteredMembers, setFilteredMembers] = useState([]);

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [membersPerPage] = useState(10);
    const [paginatedMembers, setPaginatedMembers] = useState([]);
    const [showKycModal, setShowKycModal] = useState(false);
    const [selectedMember, setSelectedMember] = useState(null);
    const [kycLoading, setKycLoading] = useState(false);
    const [kycError, setKycError] = useState('');
    const [kycSuccess, setKycSuccess] = useState('');

    // Change Agent Modal states
    const [showChangeAgentModal, setShowChangeAgentModal] = useState(false);
    const [changeAgentMember, setChangeAgentMember] = useState(null);
    const [availableAgents, setAvailableAgents] = useState([]);
    const [selectedNewAgent, setSelectedNewAgent] = useState('');
    const [changeAgentLoading, setChangeAgentLoading] = useState(false);
    const [changeAgentError, setChangeAgentError] = useState('');
    const [changeAgentSuccess, setChangeAgentSuccess] = useState('');

    // Add Member Modal states
    const [showAddMemberModal, setShowAddMemberModal] = useState(false);
    const [newMemberEmail, setNewMemberEmail] = useState('');
    const [newMemberPassword, setNewMemberPassword] = useState('');
    const [newMemberInviteCode, setNewMemberInviteCode] = useState('');
    const [addMemberLoading, setAddMemberLoading] = useState(false);
    const [addMemberError, setAddMemberError] = useState('');
    const [addMemberSuccess, setAddMemberSuccess] = useState('');

    useEffect(() => {
        const fetchMembers = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return;
            }

            // Step 1: 查询 customer_profiles
            const { data: customers, error: profileError } = await supabase
                .from('customer_profiles')
                .select('user_id, real_name, id_number, id_img_front, id_img_back, verify_status')
                .eq('agent_id', user.id)
                .order('created_at', { ascending: false });

            if (profileError || !customers) {
                console.error('Error fetching customer_profiles:', profileError);
                setLoading(false);
                return;
            }

            // Step 2: 查询 users 表
            const userIds = customers.map(c => c.user_id).filter(Boolean);

            const { data: userInfoList, error: userError } = await supabase
                .from('users')
                .select('id, email, created_at')
                .in('id', userIds);

            if (userError) {
                console.error('Error fetching users:', userError);
            }

            // Step 3: 合并结果
            const usersMap = new Map((userInfoList || []).map(u => [u.id, u]));

            const enrichedMembers = customers.map(c => ({
                ...c,
                users: usersMap.get(c.user_id) || {}
            }));

            setMembers(enrichedMembers);
            setLoading(false);
        };

        fetchMembers();
    }, []);

    // Filter members based on search criteria
    useEffect(() => {
        let filtered = members;

        // Search by username (email)
        if (searchTerm) {
            filtered = filtered.filter(member =>
                member.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                member.real_name?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by status
        if (statusFilter) {
            filtered = filtered.filter(member => member.verify_status === statusFilter);
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(member =>
                new Date(member.users?.created_at) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(member =>
                new Date(member.users?.created_at) <= new Date(endDate)
            );
        }

        setFilteredMembers(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    }, [members, searchTerm, statusFilter, startDate, endDate]);

    // Paginate filtered members
    useEffect(() => {
        const indexOfLastMember = currentPage * membersPerPage;
        const indexOfFirstMember = indexOfLastMember - membersPerPage;
        const currentMembers = filteredMembers.slice(indexOfFirstMember, indexOfLastMember);
        setPaginatedMembers(currentMembers);
    }, [filteredMembers, currentPage, membersPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(filteredMembers.length / membersPerPage);

    // Export filtered members to CSV
    const exportToCSV = () => {
        if (filteredMembers.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('username'),
            t('real_name'),
            t('id_number'),
            t('id_front_image'),
            t('id_back_image'),
            t('status'),
            t('registration_time')
        ];

        // Convert data to CSV format
        const csvData = filteredMembers.map(member => [
            member.users?.email || '-',
            member.real_name || '-',
            member.id_number || '-',
            member.id_img_front ? 'Yes' : 'No',
            member.id_img_back ? 'Yes' : 'No',
            t(member.verify_status) || 'pending',
            member.users?.created_at ? new Date(member.users.created_at).toLocaleString() : '-'
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `agent_members_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    // Fetch available agents for change agent modal
    const fetchAvailableAgents = async () => {
        const supabase = getSupabase();
        if (!supabase) return;

        try {
            // Get current user (should be an agent)
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            // Query agent_profiles and join with users to get email
            const { data: agents, error } = await supabase
                .from('agent_profiles')
                .select(`
                    user_id,
                    brand_name,
                    commission_pct,
                    users(email)
                `)
                .neq('user_id', user.id); // Exclude current agent

            if (error) {
                console.error('Error fetching agents:', error);
                return;
            }
            setAvailableAgents(agents || []);
        } catch (error) {
            console.error('Error in fetchAvailableAgents:', error);
        }
    };


    const handleAddMember = () => {
        setShowAddMemberModal(true);
        setNewMemberEmail('');
        setNewMemberPassword('');
        setNewMemberInviteCode('');
        setAddMemberError('');
        setAddMemberSuccess('');
    };

    const handleConfirmAddMember = async () => {
        if (!newMemberEmail || !newMemberPassword || !newMemberInviteCode) {
            setAddMemberError(t('all_fields_required'));
            return;
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(newMemberEmail)) {
            setAddMemberError(t('invalid_email_format'));
            return;
        }

        // Validate password length
        if (newMemberPassword.length < 6) {
            setAddMemberError(t('password_min_length'));
            return;
        }

        setAddMemberLoading(true);
        setAddMemberError('');
        setAddMemberSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user (agent)
            const { data: { user: currentUser } } = await supabase.auth.getUser();
            if (!currentUser) {
                throw new Error('Agent not authenticated');
            }

            // Step 1: Create auth user using admin API
            // Note: This requires service key, so we need to call a backend endpoint
            const createUserResponse = await fetch(`${window.wpData.apiUrl}create-member`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': window.wpData.nonce
                },
                body: JSON.stringify({
                    email: newMemberEmail,
                    password: newMemberPassword,
                    invite_code: newMemberInviteCode,
                    agent_id: currentUser.id
                })
            });

            if (!createUserResponse.ok) {
                const errorData = await createUserResponse.json();
                throw new Error(errorData.message || 'Failed to create member');
            }

            const result = await createUserResponse.json();

            if (!result.success) {
                throw new Error(t(result.error_code) || result.message || 'Failed to create member');
            }

            setAddMemberSuccess(t('member_created_successfully'));

            // Close modal and refresh after 2 seconds to show success message
            setTimeout(() => {
                setShowAddMemberModal(false);
                setNewMemberEmail('');
                setNewMemberPassword('');
                setNewMemberInviteCode('');
                // Refresh the members list
                window.location.reload();
            }, 2000);

        } catch (error) {
            console.error('Error creating member:', error);
            setAddMemberError(error.message || t('member_creation_error'));
        } finally {
            setAddMemberLoading(false);
        }
    };

    const closeAddMemberModal = () => {
        setShowAddMemberModal(false);
        setNewMemberEmail('');
        setNewMemberPassword('');
        setNewMemberInviteCode('');
        setAddMemberError('');
        setAddMemberSuccess('');
    };

    const handleKycReview = (member) => {
        setSelectedMember(member);
        setShowKycModal(true);
        setKycError('');
        setKycSuccess('');
    };

    const handleKycDecision = async (decision) => {
        if (!selectedMember) return;

        setKycLoading(true);
        setKycError('');
        setKycSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user for audit log
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('User not authenticated');
            }

            // Get the old verify_status before updating
            const { data: oldData, error: selectError } = await supabase
                .from('customer_profiles')
                .select('verify_status')
                .eq('user_id', selectedMember.user_id)
                .single();

            if (selectError) {
                console.error('Error fetching old status:', selectError);
                throw selectError;
            }

            const oldStatus = oldData?.verify_status || 'not_submitted';

            // Update the verify_status
            const { data, error } = await supabase
                .from('customer_profiles')
                .update({ verify_status: decision })
                .eq('user_id', selectedMember.user_id)
                .select();

            if (error) {
                console.error('Database error:', error);
                throw error;
            }

            // Check if any rows were updated
            if (!data || data.length === 0) {
                throw new Error('Customer profile not found or no changes made');
            }

            // Insert audit log record
            const auditLogData = {
                user_id: user.id, // Current user (agent) who made the decision
                action: decision,
                object_table: 'customer_profiles',
                object_id: selectedMember.user_id, // The customer whose KYC was reviewed
                diff: {
                    old: { verify_status: oldStatus },
                    new: { verify_status: decision }
                }
            };

            const { error: auditError } = await supabase
                .from('audit_logs')
                .insert(auditLogData);

            if (auditError) {
                console.error('Error inserting audit log:', auditError);
                // Don't throw error here as the main operation succeeded
                // Just log the error for debugging
            }

            // Update local state
            setMembers(prevMembers =>
                prevMembers.map(member =>
                    member.user_id === selectedMember.user_id
                        ? { ...member, verify_status: decision }
                        : member
                )
            );

            // Also update the selected member
            setSelectedMember(prev => ({ ...prev, verify_status: decision }));

            setKycSuccess(decision === 'approved' ? t('kyc_approved_success') : t('kyc_rejected_success'));

            // Close modal after 1.5 seconds
            setTimeout(() => {
                setShowKycModal(false);
                setSelectedMember(null);
            }, 1500);

        } catch (error) {
            console.error('Error updating KYC status:', error);
            setKycError(error.message || t('kyc_update_error'));
        } finally {
            setKycLoading(false);
        }
    };

    const closeKycModal = () => {
        setShowKycModal(false);
        setSelectedMember(null);
        setKycError('');
        setKycSuccess('');
    };

    const handleChangeAgent = async (member) => {
        setChangeAgentMember(member);
        setChangeAgentError('');
        setChangeAgentSuccess('');
        setSelectedNewAgent('');

        // Fetch available agents
        await fetchAvailableAgents();

        setShowChangeAgentModal(true);
    };

    const handleConfirmChangeAgent = async () => {
        if (!changeAgentMember || !selectedNewAgent) {
            setChangeAgentError(t('please_select_agent'));
            return;
        }

        setChangeAgentLoading(true);
        setChangeAgentError('');
        setChangeAgentSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Update customer's agent_id
            const { data, error } = await supabase
                .from('customer_profiles')
                .update({ agent_id: selectedNewAgent })
                .eq('user_id', changeAgentMember.user_id)
                .select();

            if (error) {
                console.error('Database error:', error);
                throw error;
            }

            if (!data || data.length === 0) {
                throw new Error('Failed to update agent assignment');
            }

            // Remove the member from current list since they're no longer assigned to current agent
            setMembers(prevMembers =>
                prevMembers.filter(member => member.user_id !== changeAgentMember.user_id)
            );

            setChangeAgentSuccess(t('agent_changed_successfully'));

            // Close modal after 1.5 seconds
            setTimeout(() => {
                setShowChangeAgentModal(false);
                setChangeAgentMember(null);
                setSelectedNewAgent('');
            }, 1500);

        } catch (error) {
            console.error('Error changing agent:', error);
            setChangeAgentError(error.message || t('agent_change_error'));
        } finally {
            setChangeAgentLoading(false);
        }
    };

    const closeChangeAgentModal = () => {
        setShowChangeAgentModal(false);
        setChangeAgentMember(null);
        setSelectedNewAgent('');
        setChangeAgentError('');
        setChangeAgentSuccess('');
    };


    if (loading) {
        return <div>{t('loading_members')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('member_list')}</h2>

            {/* Top Operation Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('export_data')}</Form.Label>
                                        <div>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                className="mb-2 me-2"
                                                disabled={filteredMembers.length === 0}
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export_all')}
                                            </Button>
                                            <Button
                                                variant="primary"
                                                onClick={handleAddMember}
                                                className="mb-2"
                                            >
                                                <FaPlus className="me-1" />
                                                {t('add_member')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('search_username')}</Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder={t('please_enter_username')}
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('status_filter')}</Form.Label>
                                        <Form.Select
                                            value={statusFilter}
                                            onChange={(e) => setStatusFilter(e.target.value)}
                                        >
                                            <option value="">{t('please_select_status')}</option>
                                            <option value="pending">{t('pending_review')}</option>
                                            <option value="approved">{t('approved')}</option>
                                            <option value="rejected">{t('rejected')}</option>
                                            <option value="under_review">{t('under_review')}</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Members Table */}
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('username')}</th>
                                        <th>{t('real_name')}</th>
                                        <th>{t('id_number')}</th>
                                        <th>{t('id_front_image')}</th>
                                        <th>{t('id_back_image')}</th>
                                        <th>{t('status')}</th>
                                        <th>{t('registration_time')}</th>
                                        <th>{t('actions')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedMembers.length === 0 ? (
                                        <tr>
                                            <td colSpan="9" className="text-center">{t('no_members_found')}</td>
                                        </tr>
                                    ) : (
                                        paginatedMembers.map(member => (
                                            <tr key={member.user_id}>
                                                <td>{member.users?.email || '-'}</td>
                                                <td>{member.real_name || '-'}</td>
                                                <td>{member.id_number || '-'}</td>
                                                <td>
                                                    {member.id_img_front ? (
                                                        <img
                                                            src={member.id_img_front}
                                                            alt="ID Front"
                                                            style={{
                                                                width: '60px',
                                                                height: '40px',
                                                                objectFit: 'cover',
                                                                borderRadius: '4px',
                                                                cursor: 'pointer'
                                                            }}
                                                            onClick={() => window.open(member.id_img_front, '_blank')}
                                                        />
                                                    ) : (
                                                        <span className="text-muted">-</span>
                                                    )}
                                                </td>
                                                <td>
                                                    {member.id_img_back ? (
                                                        <img
                                                            src={member.id_img_back}
                                                            alt="ID Back"
                                                            style={{
                                                                width: '60px',
                                                                height: '40px',
                                                                objectFit: 'cover',
                                                                borderRadius: '4px',
                                                                cursor: 'pointer'
                                                            }}
                                                            onClick={() => window.open(member.id_img_back, '_blank')}
                                                        />
                                                    ) : (
                                                        <span className="text-muted">-</span>
                                                    )}
                                                </td>
                                                <td><StatusBadge status={member.verify_status} type="review" /></td>
                                                <td>{member.users?.created_at ? new Date(member.users.created_at).toLocaleString() : '-'}</td>
                                                <td>
                                                    <div className="d-flex justify-content-evenly">
                                                        <Button
                                                            size="sm"
                                                            variant="outline-primary"
                                                            onClick={() => handleKycReview(member)}
                                                            title={t('kyc_review')}
                                                        >
                                                            <FaUserCheck />
                                                        </Button>
                                                        <Button
                                                            size="sm"
                                                            variant="outline-warning"
                                                            onClick={() => handleChangeAgent(member)}
                                                            title={t('change_agent')}
                                                        >
                                                            <FaExchangeAlt />
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* KYC Review Modal */}
            <Modal show={showKycModal} onHide={closeKycModal} size="lg">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('kyc_review')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeKycModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {selectedMember && (
                        <>
                            {kycError && (
                                <Alert variant="danger" className="mb-3">
                                    {kycError}
                                </Alert>
                            )}
                            {kycSuccess && (
                                <Alert variant="success" className="mb-3">
                                    {kycSuccess}
                                </Alert>
                            )}

                            <Row>
                                <Col md={6}>
                                    <Card className="mb-3">
                                        <Card.Header>
                                            <strong>{t('customer_info')}</strong>
                                        </Card.Header>
                                        <Card.Body>
                                            <p><strong>{t('username')}:</strong> {selectedMember.users?.email || '-'}</p>
                                            <p><strong>{t('real_name')}:</strong> {selectedMember.real_name || '-'}</p>
                                            <p><strong>{t('id_number')}:</strong> {selectedMember.id_number || '-'}</p>
                                            <p><strong>{t('current_status')}:</strong> <StatusBadge status={selectedMember.verify_status} type="review" /></p>
                                            <p><strong>{t('registration_time')}:</strong> {selectedMember.users?.created_at ? new Date(selectedMember.users.created_at).toLocaleString() : '-'}</p>
                                        </Card.Body>
                                    </Card>
                                </Col>
                                <Col md={6}>
                                    <Card className="mb-3">
                                        <Card.Header>
                                            <strong>{t('id_documents')}</strong>
                                        </Card.Header>
                                        <Card.Body>
                                            <div className="mb-3">
                                                <strong>{t('id_front_image')}:</strong>
                                                <div className="mt-2">
                                                    {selectedMember.id_img_front ? (
                                                        <img
                                                            src={selectedMember.id_img_front}
                                                            alt="ID Front"
                                                            style={{
                                                                width: '100%',
                                                                maxHeight: '150px',
                                                                objectFit: 'contain',
                                                                borderRadius: '4px',
                                                                cursor: 'pointer',
                                                                border: '1px solid #dee2e6'
                                                            }}
                                                            onClick={() => window.open(selectedMember.id_img_front, '_blank')}
                                                        />
                                                    ) : (
                                                        <div className="text-muted text-center py-3" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>
                                                            {t('no_image_uploaded')}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="mb-3">
                                                <strong>{t('id_back_image')}:</strong>
                                                <div className="mt-2">
                                                    {selectedMember.id_img_back ? (
                                                        <img
                                                            src={selectedMember.id_img_back}
                                                            alt="ID Back"
                                                            style={{
                                                                width: '100%',
                                                                maxHeight: '150px',
                                                                objectFit: 'contain',
                                                                borderRadius: '4px',
                                                                cursor: 'pointer',
                                                                border: '1px solid #dee2e6'
                                                            }}
                                                            onClick={() => window.open(selectedMember.id_img_back, '_blank')}
                                                        />
                                                    ) : (
                                                        <div className="text-muted text-center py-3" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>
                                                            {t('no_image_uploaded')}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </Card.Body>
                                    </Card>
                                </Col>
                            </Row>
                        </>
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeKycModal} disabled={kycLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="danger"
                        onClick={() => handleKycDecision('rejected')}
                        disabled={kycLoading || selectedMember?.verify_status === 'rejected'}
                    >
                        {kycLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('processing')}
                            </>
                        ) : (
                            <>
                                <FaTimes className="me-1" />
                                {t('reject')}
                            </>
                        )}
                    </Button>
                    <Button
                        variant="success"
                        onClick={() => handleKycDecision('approved')}
                        disabled={kycLoading || selectedMember?.verify_status === 'approved'}
                    >
                        {kycLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('processing')}
                            </>
                        ) : (
                            <>
                                <FaCheck className="me-1" />
                                {t('approve')}
                            </>
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Change Agent Modal */}
            <Modal show={showChangeAgentModal} onHide={closeChangeAgentModal} size="md">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('change_agent')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeChangeAgentModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {changeAgentError && (
                        <Alert variant="danger" className="mb-3">
                            {changeAgentError}
                        </Alert>
                    )}
                    {changeAgentSuccess && (
                        <Alert variant="success" className="mb-3">
                            {changeAgentSuccess}
                        </Alert>
                    )}

                    {changeAgentMember && (
                        <div className="mb-4">
                            <Card>
                                <Card.Header>
                                    <strong>{t('customer_info')}</strong>
                                </Card.Header>
                                <Card.Body>
                                    <p><strong>{t('username')}:</strong> {changeAgentMember.users?.email || '-'}</p>
                                    <p><strong>{t('real_name')}:</strong> {changeAgentMember.real_name || '-'}</p>
                                    <p><strong>{t('current_status')}:</strong> <StatusBadge status={changeAgentMember.verify_status} type="review" /></p>
                                </Card.Body>
                            </Card>
                        </div>
                    )}

                    <Form.Group className="mb-3">
                        <Form.Label><strong>{t('select_new_agent')}</strong></Form.Label>
                        <Form.Select
                            value={selectedNewAgent}
                            onChange={(e) => setSelectedNewAgent(e.target.value)}
                            disabled={changeAgentLoading}
                        >
                            <option value="">{t('please_select_agent')}</option>
                            {availableAgents.map(agent => (
                                <option key={agent.user_id} value={agent.user_id}>
                                    {agent.brand_name || agent.users?.email || agent.user_id}
                                    {agent.commission_pct && ` (${agent.commission_pct}%)`}
                                </option>
                            ))}
                        </Form.Select>
                        {availableAgents.length === 0 && (
                            <Form.Text className="text-muted">
                                {t('no_available_agents')}
                            </Form.Text>
                        )}
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeChangeAgentModal} disabled={changeAgentLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleConfirmChangeAgent}
                        disabled={changeAgentLoading || !selectedNewAgent || availableAgents.length === 0}
                    >
                        {changeAgentLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('processing')}
                            </>
                        ) : (
                            <>
                                <FaExchangeAlt className="me-1" />
                                {t('confirm_change')}
                            </>
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Add Member Modal */}
            <Modal show={showAddMemberModal} onHide={closeAddMemberModal} size="md">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('add_member')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeAddMemberModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {addMemberError && (
                        <Alert variant="danger" className="mb-3">
                            {addMemberError}
                        </Alert>
                    )}
                    {addMemberSuccess && (
                        <Alert variant="success" className="mb-3">
                            {addMemberSuccess}
                        </Alert>
                    )}

                    <Form>
                        <Form.Group className="mb-3">
                            <Form.Label><strong>{t('email_address')}</strong></Form.Label>
                            <Form.Control
                                type="email"
                                value={newMemberEmail}
                                onChange={(e) => setNewMemberEmail(e.target.value)}
                                placeholder={t('enter_email_address')}
                                disabled={addMemberLoading}
                                required
                            />
                            <Form.Text className="text-muted">
                                {t('member_email_help')}
                            </Form.Text>
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label><strong>{t('password')}</strong></Form.Label>
                            <Form.Control
                                type="password"
                                value={newMemberPassword}
                                onChange={(e) => setNewMemberPassword(e.target.value)}
                                placeholder={t('enter_password')}
                                disabled={addMemberLoading}
                                minLength={6}
                                required
                            />
                            <Form.Text className="text-muted">
                                {t('password_min_6_chars')}
                            </Form.Text>
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label><strong>{t('invite_code')}</strong></Form.Label>
                            <Form.Control
                                type="text"
                                value={newMemberInviteCode}
                                onChange={(e) => setNewMemberInviteCode(e.target.value)}
                                placeholder={t('enter_invite_code')}
                                disabled={addMemberLoading}
                                required
                            />
                            <Form.Text className="text-muted">
                                {t('invite_code_help')}
                            </Form.Text>
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeAddMemberModal} disabled={addMemberLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleConfirmAddMember}
                        disabled={addMemberLoading || !newMemberEmail || !newMemberPassword || !newMemberInviteCode}
                    >
                        {addMemberLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('creating')}
                            </>
                        ) : (
                            <>
                                <FaPlus className="me-1" />
                                {t('create_member')}
                            </>
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default Members;