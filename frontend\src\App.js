import React, { useEffect, useState } from 'react';
import { initSupabase } from './supabaseClient';
import {
  HashRouter,
  Routes,
  Route,
  Link,
  Navigate,
  useLocation,
} from 'react-router-dom';
import { Container, Navbar, Nav, NavDropdown } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { FaSignOutAlt, FaTachometerAlt, FaHardHat, FaUser, FaGlobe, FaCoins, FaChartBar, FaFileInvoiceDollar, FaUsers, FaShoppingBag, FaYenSign, FaBuffer, FaKey, FaUserFriends } from 'react-icons/fa';

// Direct imports instead of lazy loading for better compatibility
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import ResetPasswordPage from './pages/ResetPasswordPage';
// import CustomerDashboard from './pages/customer/Dashboard';
import ProductListPage from './pages/customer/ProductListPage';
import MyAccountPage from './pages/customer/MyAccountPage';
import MyGainsPage from './pages/customer/MyGainsPage';
import KycPage from './pages/customer/KycPage';
import RecommendPage from './pages/common/RecommendPage';
import ChangeLoginPass from './pages/common/ChangeLoginPass';
import ChangeWithdrawPass from './pages/customer/ChangeWithdrawPass';
import AgentDashboard from './pages/agent/Dashboard';
import Members from './pages/agent/Members';
import Recommend from './pages/agent/Recommend';
import AgentProductListPage from './pages/agent/AgentProductListPage';
import MakerDashboard from './pages/maker/Dashboard';
import MakerProductListPage from './pages/maker/MakerProductListPage';
import MakerOrderListPage from './pages/maker/MakerOrderListPage';
import MakerFacilityListPage from './pages/maker/MakerFacilities';
import MakerMinerListPage from './pages/maker/MakerMiners';
import MinerEarnings from './pages/maker/MinerEarnings';
import MinerSnapshots from './pages/maker/MinerSnapshots';
import Transactions from './pages/maker/Transactions';
import CoinBatches from './pages/maker/CoinBatches';
import NetworkStats from './pages/maker/NetworkStats';
import CustomerAssets from './pages/maker/CustomerAssets';
import OrderReports from './pages/maker/OrderReports';
import OrderDistributions from './pages/maker/OrderDistributions';
import CapacityRequest from './pages/maker/CapacityRequest';
import ManualDeposits from './pages/maker/ManualDeposits';
import MakerWithdrawList from './pages/maker/WithdrawList';
import AgentCapacityRequest from './pages/agent/CapacityRequest';
import AgentNetworkStats from './pages/agent/NetworkStats';
import WalletFlow from './pages/agent/WalletFlow';
import AgentOrderReports from './pages/agent/OrderReports';
import WithdrawList from './pages/agent/WithdrawList';
// import CustomerFilfox from './pages/customer/Filfox';
import MakerRecommend from './pages/maker/MakerRecommend';
import MakerMembers from './pages/maker/MakerMembers';

function App() {
  const { t, i18n } = useTranslation();
  const [supabase, setSupabase] = useState(null);
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  // 将 role 改为 state，这样可以动态更新
  const [role, setRole] = useState(localStorage.getItem('user_role'));

  useEffect(() => {
    const initialize = async () => {
      const supa = await initSupabase();
      setSupabase(supa);

      const { data: { session } } = await supa.auth.getSession();
      setSession(session);

      supa.auth.onAuthStateChange((_event, newSession) => {
        setSession(newSession);
        if (!newSession) {
          localStorage.removeItem('user_role');
          setRole(null); // 清空 role state
        } else {
          // 当有新的 session 时，重新读取 role
          const currentRole = localStorage.getItem('user_role');
          setRole(currentRole);
        }
      });

      setLoading(false);
    };
    initialize();
  }, []);

  // 监听 localStorage 的变化来更新 role
  useEffect(() => {
    const handleStorageChange = () => {
      const currentRole = localStorage.getItem('user_role');
      setRole(currentRole);
    };

    // 监听 storage 事件（用于跨标签页同步）
    window.addEventListener('storage', handleStorageChange);

    // 也可以添加一个自定义事件来处理同一标签页内的更新
    window.addEventListener('roleUpdated', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('roleUpdated', handleStorageChange);
    };
  }, []);

  const changeLanguage = (lng) => {
    i18n.changeLanguage(lng);
  };

  // Logout function
  const handleLogout = async () => {
    // Clear localStorage
    localStorage.clear();
    setRole(null); // 清空 role state
    
    // Sign out from Supabase
    if (supabase) {
      await supabase.auth.signOut();
    }
    
    // Redirect to login page
    window.location.href = '#/login';
  };

  // Debug: Log current URL and hash
  React.useEffect(() => {
  }, []);

  // Require login to access protected pages
  const RequireAuth = ({ children }) => {
    const location = useLocation();
    if (!session) {
      return <Navigate to="/login" state={{ from: location }} replace />;
    }
    return children;
  };

  // Auto redirect from "/" based on role
  const RoleRedirect = () => {
    if (role === 'maker') return <Navigate to="/maker" replace />;
    if (role === 'agent') return <Navigate to="/agent" replace />;
    if (role === 'customer') return <Navigate to="/my" replace />; // 客户直接跳转到my页面
    if (role === 'technician') return <Navigate to="/maker" replace />; // technician uses maker pages
    return <Navigate to="/login" replace />; // default to login page
  };

  return (
    <HashRouter>
      <div className="fil-platform-app">
        <Navbar bg="light" variant="light" expand="lg" className="modern-navbar">
          <Container fluid>
            {/* FIL Logo and Brand - REMOVED */}
            
            <Navbar.Toggle aria-controls="basic-navbar-nav" />
            <Navbar.Collapse id="basic-navbar-nav">
              <Nav className="me-auto">
                {/* 只为maker、agent和technician显示dashboard链接 */}
                {(role === 'maker' || role === 'agent' || role === 'technician') && (
                  <Nav.Link href="#/" className="nav-link-modern">
                    <FaTachometerAlt className="me-2" />
                    {t('dashboard')}
                  </Nav.Link>
                )}
                
                {/* ===== ★ Maker 导航开始 ★ ===== */}
                {(role === 'maker' || role === 'technician') && (
                  <>
                    {/* Miner Management 下拉 */}
                    <NavDropdown
                      title={
                        <>
                          <FaHardHat className="me-2" />
                          {t('miner_management')}
                        </>
                      }
                      id="maker-miner-dropdown"
                      className="nav-dropdown-modern"
                    >
                      <NavDropdown.Item as={Link} to="/maker/miners" className="dropdown-item-modern">
                        {t('miner_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/facilities" className="dropdown-item-modern">
                        {t('facility_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/earnings" className="dropdown-item-modern">
                        {t('earnings_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/transfers" className="dropdown-item-modern">
                        {t('transfer_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/snapshots" className="dropdown-item-modern">
                        {t('daily_snapshot')}
                      </NavDropdown.Item>
                    </NavDropdown>

                    <NavDropdown title={
                        <>
                          <FaGlobe className="me-2" />
                          {t('operations_management')}
                        </>
                      }
                      id="maker-operations-dropdown"
                      className="nav-dropdown-modern"
                    >
                      <NavDropdown.Item as={Link} to="/maker/capacity" className="dropdown-item-modern">
                        {t('capacity_expansion_request')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/orders" className="dropdown-item-modern">
                        {t('maker_orders')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/members" className="dropdown-item-modern">
                        {t('member_management')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/manual-deposits" className="dropdown-item-modern">
                        {t('manual_deposit')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/withdraw-list" className="dropdown-item-modern">
                        {t('withdraw_list')}
                      </NavDropdown.Item>
                    </NavDropdown>

                    <NavDropdown title={
                        <>
                          <FaCoins className="me-2" />
                          {t('coin_management')}
                        </>
                      }
                      id="maker-coin-dropdown"
                      className="nav-dropdown-modern"
                    >
                      <NavDropdown.Item as={Link} to="/maker/coin-batches" className="dropdown-item-modern">
                        {t('coin_batches')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/network-stats" className="dropdown-item-modern">
                        {t('network_stats')}
                      </NavDropdown.Item>
                    </NavDropdown>

                    <NavDropdown title={
                        <>
                          <FaChartBar className="me-2" />
                          {t('report_management')}
                        </>
                      }
                      id="maker-report-dropdown"
                      className="nav-dropdown-modern"
                    >
                      <NavDropdown.Item as={Link} to="/maker/customer-assets" className="dropdown-item-modern">
                        {t('customer_assets')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/recommend" className="dropdown-item-modern">
                        {t('recommendation')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/order-reports" className="dropdown-item-modern">
                        {t('order_reports')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/maker/order-distributions" className="dropdown-item-modern">
                        {t('order_distributions')}
                      </NavDropdown.Item>
                    </NavDropdown>
                  </>
                )}
                {/* ===== ★ Maker 导航结束 ★ ===== */}

                {/* ===== ★ Agent 导航开始 ★ ===== */}
                {role === 'agent' && (
                  <>
                    <NavDropdown title={
                        <>
                          <FaFileInvoiceDollar className="me-2" />
                          {t('profit_management')}
                        </>
                      }
                      id="agent-profit-dropdown"
                      className="nav-dropdown-modern"
                    >
                      <NavDropdown.Item as={Link} to="/agent/profit" className="dropdown-item-modern">
                        {t('profit_records')}
                      </NavDropdown.Item>
                    </NavDropdown>

                    <NavDropdown title={
                        <>
                          <FaUsers className="me-2" />
                          {t('member_management')}
                        </>
                      }
                      id="agent-member-dropdown"
                      className="nav-dropdown-modern"
                    >
                      <NavDropdown.Item as={Link} to="/agent/member-list" className="dropdown-item-modern">
                        {t('member_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/agent/recommendation" className="dropdown-item-modern">
                        {t('recommendation')}
                      </NavDropdown.Item>
                    </NavDropdown>

                    <NavDropdown title={
                        <>
                          <FaShoppingBag className="me-2" />
                          {t('product_management')}
                        </>
                      }
                      id="agent-product-dropdown"
                      className="nav-dropdown-modern"
                    >
                      <NavDropdown.Item as={Link} to="/agent/products" className="dropdown-item-modern">
                        {t('product_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/agent/power" className="dropdown-item-modern">
                        {t('capacity_expansion_request')}
                      </NavDropdown.Item>
                    </NavDropdown>

                    <NavDropdown title={
                        <>
                          <FaYenSign className="me-2" />
                          {t('finance_management')}
                        </>
                      }
                      id="agent-finance-dropdown"
                      className="nav-dropdown-modern"
                    >
                      <NavDropdown.Item as={Link} to="/agent/wallet-flow-list" className="dropdown-item-modern">
                        {t('wallet_flow')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/agent/withdraw-list" className="dropdown-item-modern">
                        {t('withdraw_list')}
                      </NavDropdown.Item>
                      <NavDropdown.Item as={Link} to="/agent/order-list" className="dropdown-item-modern">
                        {t('order_list')}
                      </NavDropdown.Item>
                    </NavDropdown>
                  </>
                )}
                {/* ===== ★ Agent 导航结束 ★ ===== */}

                {/* ===== ★ Customer 导航开始 ★ ===== */}
                {role === 'customer' && (
                  <>
                    {/* <Nav.Link as={Link} to="/customer/filfox" className="nav-link-modern">
                      <FaGlobe className="me-2" />
                      FilFox
                    </Nav.Link> */}
                    <Nav.Link as={Link} to="/my" className="nav-link-modern">
                      <FaUser className="me-2" />
                      {t('my_page')}
                    </Nav.Link>
                    <Nav.Link as={Link} to="/my-gains" className="nav-link-modern">
                      <FaBuffer className="me-2" />
                      {t('assets')}
                    </Nav.Link>
                    
                  </>
                )}
                {/* ===== ★ Customer 导航结束 ★ ===== */}
                {/* Add other nav links based on role later */}
              </Nav>
              <Nav>
                <NavDropdown title={t('language')} id="basic-nav-dropdown" className="nav-dropdown-modern">
                  <NavDropdown.Item onClick={() => changeLanguage('ja')} className="dropdown-item-modern">日本語</NavDropdown.Item>
                  <NavDropdown.Item onClick={() => changeLanguage('zh')} className="dropdown-item-modern">中文</NavDropdown.Item>
                  <NavDropdown.Item onClick={() => changeLanguage('en')} className="dropdown-item-modern">English</NavDropdown.Item>
                </NavDropdown>
                {/* User Menu */}
                {session && (
                  <NavDropdown
                    title={<FaUser className="me-2"/>}
                    id="user-menu-dropdown"
                    className="nav-dropdown-modern"
                  >
                    <NavDropdown.Item as={Link} to="/change-login-pass" className="dropdown-item-modern">
                      <FaKey className="me-2" />
                      {t('change_login_password')}
                    </NavDropdown.Item>
                    <NavDropdown.Item as={Link} to="/recommend" className="dropdown-item-modern">
                      <FaUserFriends className="me-2" />
                      {t('my_recommendations')}
                    </NavDropdown.Item>
                    <NavDropdown.Divider />
                    <NavDropdown.Item onClick={handleLogout} className="dropdown-item-modern">
                      <FaSignOutAlt className="me-2" />
                      {t('logout')}
                    </NavDropdown.Item>
                  </NavDropdown>
                )}
              </Nav>
            </Navbar.Collapse>
          </Container>
        </Navbar>

        <Container fluid className="main-container">
          {loading ? (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>{t('initializing_platform')}</p>
            </div>
          ) : !supabase ? (
            <div className="alert alert-danger connection-error">
              <h4>Connection Error</h4>
              <p>{t('backend_connection_failed')}</p>
            </div>
          ) : (
            <div className="content-wrapper">
              <Routes>
              {/* Public Routes */}
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register/:inviteCode" element={<RegisterPage />} />
              <Route path="/reset-password" element={<ResetPasswordPage />} />

              {/* Root path → redirect by role */}
              <Route path="/" element={<RequireAuth><RoleRedirect /></RequireAuth>} />

              {/* Customer Routes */}
              {/* <Route path="/customer" element={<RequireAuth><CustomerDashboard /></RequireAuth>} /> */}
              {/* <Route path="/customer/filfox" element={<RequireAuth><CustomerFilfox /></RequireAuth>} /> */}
              <Route path="/products" element={<RequireAuth><ProductListPage /></RequireAuth>} />
              <Route path="/my" element={<RequireAuth><MyAccountPage /></RequireAuth>} />
              <Route path="/my-gains" element={<RequireAuth><MyGainsPage /></RequireAuth>} />
              <Route path="/my/kyc" element={<RequireAuth><KycPage /></RequireAuth>} />
              <Route path="/my/recommend" element={<RequireAuth><RecommendPage /></RequireAuth>} />
              <Route path="/my/change-login-pass" element={<RequireAuth><ChangeLoginPass /></RequireAuth>} />
              <Route path="/my/change-withdraw-pass" element={<RequireAuth><ChangeWithdrawPass /></RequireAuth>} />

              {/* Common Routes - accessible by all roles */}
              <Route path="/change-login-pass" element={<RequireAuth><ChangeLoginPass /></RequireAuth>} />
              <Route path="/recommend" element={<RequireAuth><RecommendPage /></RequireAuth>} />

              {/* Agent Routes */}
              <Route path="/agent" element={<RequireAuth><AgentDashboard /></RequireAuth>} />
              <Route path="/agent/member-list" element={<RequireAuth><Members /></RequireAuth>} />
              <Route path="/agent/recommendation" element={<RequireAuth><Recommend /></RequireAuth>} />
              <Route path="/agent/products" element={<RequireAuth><AgentProductListPage /></RequireAuth>} />
              <Route path="/agent/power" element={<RequireAuth><AgentCapacityRequest /></RequireAuth>} />
              <Route path="/agent/profit" element={<RequireAuth><AgentNetworkStats /></RequireAuth>} />
              <Route path="/agent/wallet-flow-list" element={<RequireAuth><WalletFlow /></RequireAuth>} />
              <Route path="/agent/order-list" element={<RequireAuth><AgentOrderReports /></RequireAuth>} />
              <Route path="/agent/withdraw-list" element={<RequireAuth><WithdrawList /></RequireAuth>} />

              {/* Maker Routes - accessible by both maker and technician */}
                <Route path="/maker" element={<RequireAuth><MakerDashboard /></RequireAuth>} />
                <Route path="/maker/products" element={<RequireAuth><MakerProductListPage /></RequireAuth>} />
                <Route path="/maker/orders" element={<RequireAuth><MakerOrderListPage /></RequireAuth>} />
                <Route path="/maker/facilities" element={<RequireAuth><MakerFacilityListPage /></RequireAuth>} />
                <Route path="/maker/miners" element={<RequireAuth><MakerMinerListPage /></RequireAuth>} />
                <Route path="/maker/earnings" element={<RequireAuth><MinerEarnings /></RequireAuth>} />
                <Route path="/maker/transfers" element={<RequireAuth><Transactions /></RequireAuth>} />
                <Route path="/maker/snapshots" element={<RequireAuth><MinerSnapshots /></RequireAuth>} />
                <Route path="/maker/coin-batches" element={<RequireAuth><CoinBatches /></RequireAuth>} />
                <Route path="/maker/network-stats" element={<RequireAuth><NetworkStats /></RequireAuth>} />
                <Route path="/maker/customer-assets" element={<RequireAuth><CustomerAssets /></RequireAuth>} />
                <Route path="/maker/order-reports" element={<RequireAuth><OrderReports /></RequireAuth>} />
                <Route path="/maker/order-distributions" element={<RequireAuth><OrderDistributions /></RequireAuth>} />
                <Route path="/maker/capacity" element={<RequireAuth><CapacityRequest /></RequireAuth>} />
                <Route path="/maker/manual-deposits" element={<RequireAuth><ManualDeposits /></RequireAuth>} />
                <Route path="/maker/withdraw-list" element={<RequireAuth><MakerWithdrawList /></RequireAuth>} />
                <Route path="/maker/recommend" element={<RequireAuth><MakerRecommend /></RequireAuth>} />
                <Route path="/maker/members" element={<RequireAuth><MakerMembers /></RequireAuth>} />
              
              {/* Fallback */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
            </div>
          )}
        </Container>
      </div>
    </HashRouter>
  );
}

export default App;